<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <title>我的书架</title>
    <link rel="stylesheet" href="style/reset.css">
    <link rel="stylesheet" href="style/common.css">
    <link rel="stylesheet" href="style/index.css">
    <link rel="stylesheet" href="style/loading.css">
</head>
<body class="wr_whiteTheme">

<div class="navBar">
    <div class="navBar_inner">
        <h2 class="navBar_title">我的书架<span class="total"></span></h2>
        <div class="navBar_menu">
            <a class="navBar_link current">我的书架</a>
            <span class="navBar_separator"></span>
            <a href="/read.html" class="navBar_link">自动阅读</a>
            <span class="navBar_separator"></span>
            <a href="https://github.com/champkeh/wereadx" target="_blank" class="navBar_link">GitHub</a>
        </div>
    </div>
</div>

<div class="page_container">
    <div class="bookshelf_preview_container">
        <div class="readerChapterContentLoading">
            <div class="wr_loading">
                <div class="wr_loading_line" style="transform:scale(0.8, 0.8);">
                    <div class="wr_loading_line_1"></div>
                    <div class="wr_loading_line_2"></div>
                    <div class="wr_loading_line_3"></div>
                    <div class="wr_loading_line_4"></div>
                    <div class="wr_loading_line_5"></div>
                    <div class="wr_loading_line_6"></div>
                    <div class="wr_loading_line_7"></div>
                    <div class="wr_loading_line_8"></div>
                    <div class="wr_loading_line_9"></div>
                    <div class="wr_loading_line_10"></div>
                    <div class="wr_loading_line_11"></div>
                    <div class="wr_loading_line_12"></div>
                </div>
            </div>
        </div>

        <div id="books" class="bookshelf_preview_body"></div>
    </div>
</div>

<a title="建议反馈" class="feedback" href="https://github.com/champkeh/weread-download/issues/new" target="_blank">
    <svg data-v-98989b36="" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
         class="">
        <path data-v-98989b36=""
              d="M3.26628 5.18082C3.26628 4.35104 3.94716 3.67545 4.7907 3.67545H15.2093C16.0528 3.67545 16.7337 4.35104 16.7337 5.18082V12.538C16.7337 13.3678 16.0528 14.0434 15.2093 14.0434H11.6358C11.4624 14.0434 11.2948 14.1064 11.165 14.221L8.28954 16.7575V14.7452C8.28954 14.356 7.97087 14.0434 7.5814 14.0434H4.7907C3.94716 14.0434 3.26628 13.3678 3.26628 12.538V5.18082ZM4.7907 2.27187C3.16822 2.27187 1.85 3.57264 1.85 5.18082V12.538C1.85 14.1462 3.16822 15.447 4.7907 15.447H6.87326V17.5004C6.87326 18.4274 7.97825 18.9111 8.67204 18.2991L11.9053 15.447H15.2093C16.8318 15.447 18.15 14.1462 18.15 12.538V5.18082C18.15 3.57264 16.8318 2.27187 15.2093 2.27187H4.7907Z"
              fill="#1E80FF" stroke="#1E80FF" stroke-width="0.3" stroke-linejoin="round"></path>
        <path data-v-98989b36=""
              d="M7.00132 9.49204C6.79163 9.17668 6.366 9.09101 6.05063 9.3007C5.73527 9.51038 5.6496 9.93602 5.85929 10.2514C6.74878 11.5891 8.27183 12.4732 10.0017 12.4732C11.7316 12.4732 13.2546 11.5891 14.1441 10.2514C14.3538 9.93602 14.2681 9.51038 13.9528 9.3007C13.6374 9.09101 13.2118 9.17668 13.0021 9.49204C12.356 10.4637 11.2532 11.1018 10.0017 11.1018C8.75017 11.1018 7.64741 10.4637 7.00132 9.49204Z"
              fill="#1E80FF" stroke="#1E80FF" stroke-width="0.3" stroke-linecap="round" stroke-linejoin="round"></path>
    </svg>
</a>

<script>
    function gotoLogin() {
        localStorage.removeItem('token')
        window.location.href = '/login.html'
    }

    function getToken() {
        const token = localStorage.getItem('token')
        if (!token) {
            gotoLogin()
        } else {
            return token
        }
    }

    function fetchBookList() {
        const token = getToken()

        fetch('/api/shelf/book/list', {
            headers: {
                token: token,
            }
        }).then(resp => resp.json()).then(resp => {
            const {code, data, msg} = resp
            if (code === 2) {
                gotoLogin()
                return
            } else if (code !== 0) {
                alert(msg)
                return
            }

            const {books, bookCount} = data
            document.querySelector('.total').textContent = `(共${bookCount}本书)`
            document.querySelector('.readerChapterContentLoading').style.display = 'none'
            books.sort((a, b) => b.readUpdateTime - a.readUpdateTime)
            renderBooks(books)
        })
    }

    function renderBooks(books) {
        const fragment = document.createDocumentFragment()
        for (const book of books) {
            // div.bookshelf_preview_item
            const previewItem = document.createElement('div')
            previewItem.className = 'bookshelf_preview_item ring-1'
            previewItem.title = book.title
            fragment.append(previewItem)

            // a.bookshelf_preview_item_link
            const previewItemLink = document.createElement('a')
            previewItemLink.className = 'bookshelf_preview_item_link'
            previewItemLink.href = `/detail.html?bookId=${book.bookId}`
            previewItem.append(previewItemLink)

            // div.bookshelf_preview_item_container
            const previewItemContainer = document.createElement('div')
            previewItemContainer.className = 'bookshelf_preview_item_container'
            previewItem.append(previewItemContainer)

            // div.bookshelf_preview_cover
            const previewCover = document.createElement('div')
            previewCover.className = 'wr_bookCover bookshelf_preview_cover'
            previewItemContainer.append(previewCover)
            const bookCoverImg = document.createElement('img')
            bookCoverImg.className = 'wr_bookCover_img'
            bookCoverImg.alt = '书籍封面'
            // 替换成高清图片
            const re = /(.+)\/s_([^/]+)$/
            if (re.test(book.cover)) {
                book.cover = book.cover.replace(/(.+)\/s_([^/]+)$/, '$1/t6_$2')
            }
            bookCoverImg.src = book.cover
            previewCover.append(bookCoverImg)
            const bookCoverBorder = document.createElement('div')
            bookCoverBorder.className = 'wr_bookCover_border'
            previewCover.append(bookCoverBorder)
            const bookCoverDecor = document.createElement('span')
            bookCoverDecor.className = 'wr_bookCover_decor wr_bookCover_gradientDecor wr_bookCover_borderDecor'
            previewCover.append(bookCoverDecor)

            // div.bookshelf_preview_content
            const previewContent = document.createElement('div')
            previewContent.className = 'bookshelf_preview_content'
            previewItemContainer.append(previewContent)
            const previewTitle = document.createElement('p')
            previewTitle.className = 'bookshelf_preview_title'
            previewTitle.textContent = book.title
            previewContent.append(previewTitle)
            const div = document.createElement('div')
            const previewAuthor = document.createElement('span')
            previewAuthor.className = 'bookshelf_preview_author'
            previewAuthor.textContent = book.author
            div.append(previewAuthor)
            previewContent.append(div)
        }

        document.querySelector('#books').append(fragment)
    }

    window.addEventListener('DOMContentLoaded', fetchBookList)
</script>
</body>
</html>

.readerChapterContentLoading {
    position: relative;
    height: 80px;
    margin-top: 20%;
}

.readerChapterContentLoading .wr_loading {
    left: 50%;
    top: 40%;
}

.wr_loading {
    position: absolute;
}

.wr_loading_line {
    position: relative;
}

@keyframes wr_loading {
    0% {
        opacity: 1
    }

    to {
        opacity: .25
    }
}

.wr_loading_line div {
    width: 5px;
    height: 20px;
    position: absolute;
    left: 100%;
    top: 100%;
    animation: wr_loading 1.2s linear infinite;
    background: #B2B4B8;
}

.wr_loading_line .wr_loading_line_1 {
    transform: rotate(0deg) translateY(-34px);
    animation-delay: 0s;
    opacity: 0
}

.wr_loading_line .wr_loading_line_2 {
    transform: rotate(30deg) translateY(-34px);
    animation-delay: .1s;
    opacity: .0833333333
}

.wr_loading_line .wr_loading_line_3 {
    transform: rotate(60deg) translateY(-34px);
    animation-delay: .2s;
    opacity: .1666666667
}

.wr_loading_line .wr_loading_line_4 {
    transform: rotate(90deg) translateY(-34px);
    animation-delay: .3s;
    opacity: .25
}

.wr_loading_line .wr_loading_line_5 {
    transform: rotate(120deg) translateY(-34px);
    animation-delay: .4s;
    opacity: .3333333333
}

.wr_loading_line .wr_loading_line_6 {
    transform: rotate(150deg) translateY(-34px);
    animation-delay: .5s;
    opacity: .4166666667
}

.wr_loading_line .wr_loading_line_7 {
    transform: rotate(180deg) translateY(-34px);
    animation-delay: .6s;
    opacity: .5
}

.wr_loading_line .wr_loading_line_8 {
    transform: rotate(210deg) translateY(-34px);
    animation-delay: .7s;
    opacity: .5833333333
}

.wr_loading_line .wr_loading_line_9 {
    transform: rotate(240deg) translateY(-34px);
    animation-delay: .8s;
    opacity: .6666666667
}

.wr_loading_line .wr_loading_line_10 {
    transform: rotate(270deg) translateY(-34px);
    animation-delay: .9s;
    opacity: .75
}

.wr_loading_line .wr_loading_line_11 {
    transform: rotate(300deg) translateY(-34px);
    animation-delay: 1s;
    opacity: .8333333333
}

.wr_loading_line .wr_loading_line_12 {
    transform: rotate(330deg) translateY(-34px);
    animation-delay: 1.1s;
    opacity: .9166666667
}

import {runInDenoDeploy} from "../utils/index.ts";
import {dotenv, postgres} from "../deps.ts"

let sql: any = null;

async function getSql() {
    if (sql) {
        return sql;
    }

    const env = await dotenv.load()
    let databaseUrl: string

    if (runInDenoDeploy()) {
        databaseUrl = Deno.env.get("DATABASE_URL") || "";
    } else {
        databaseUrl = env["DATABASE_URL"] || "";
    }

    if (!databaseUrl) {
        throw new Error("DATABASE_URL environment variable is not set");
    }

    sql = postgres.default(databaseUrl, {
        onnotice: () => {},
    })

    return sql;
}

export default getSql;

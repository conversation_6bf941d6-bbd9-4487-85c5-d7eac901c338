<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <title>自动阅读</title>
    <link rel="stylesheet" href="style/reset.css">
    <link rel="stylesheet" href="style/common.css">
    <style>
        .page_inner {
            display: flex;
            flex-direction: row;
            overflow: hidden;
            height: calc(100vh - 70px);
            border-radius: 0;
            border: solid rgba(0, 0, 0, .05);
            border-width: 0 0 1px;
            background-color: #FFF;
        }
        .rank_panel {
            width: 500px;
            display: flex;
            flex-direction: column;
            align-items: stretch;

            .title {
                font-size: 24px;
                padding: 1em 0;
                text-align: center;
                box-shadow: rgba(0, 0, 0, 0.1) 0 4px 9px 0;

                & span {
                    font-size: 14px;
                }
            }

            #rank {
                overflow-x: hidden;
                overflow-y: auto;
                height: calc(100vh - 180px);
                padding: 0 20px;
                .item {
                    display: flex;
                    align-items: center;
                    margin: 20px;
                    font-size: 18px;

                    .no {
                        font-size: 20px;
                        width: 2em;
                        font-weight: bold;
                        color: darkorange;
                    }
                    .avatar {
                        display: block;
                        width: 50px;
                        height: 50px;
                        margin-right: 20px;
                        border-radius: 50%;
                    }
                    .name {
                        flex: 1;
                    }
                    .score {
                    }
                }
            }
        }
        .detail_panel {
            flex: 1;
            padding: 0 20px;
            border-left: 1px dashed rgba(0, 0, 0, 0.1);
            font-size: 16px;

            .title {
                font-size: 20px;
                /*padding: 1em;*/
                margin-bottom: .5em;
            }
            .tip_panel {
                border-left: 6px solid #e96c47;
                padding: 1em;

                & p {
                    margin: .5em 0;
                }
                & strong {
                    color: #e96c47;
                }
            }
            .task_panel {
                margin-top: 2em;
                padding: 2em 1em;
                border-top: 1px solid #dcdcdc;

                .head {
                    display: flex;
                    justify-content: space-between;

                    .btn {
                        display: none;
                        cursor: pointer;
                        &[disabled] {
                            cursor: not-allowed;
                            opacity: .8;
                        }
                    }
                }

                .card {
                    display: none;
                    border: 2px solid rebeccapurple;
                    padding: 1em;
                    border-radius: 5px;
                    & p {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        height: 2em;
                        font-family: "Fira Mono", "DejaVu Sans Mono", Menlo, Consolas, "Liberation Mono", Monaco, "Lucida Console", monospace;
                    }
                }
                .empty {
                    display: none;
                }
            }
        }
    </style>
</head>
<body>

<div class="navBar">
    <div class="navBar_inner">
        <h2 class="navBar_title">自动阅读<span class="total"></span></h2>
        <div class="navBar_menu">
            <a href="/" class="navBar_link">我的书架</a>
            <span class="navBar_separator"></span>
            <a class="navBar_link current">自动阅读</a>
            <span class="navBar_separator"></span>
            <a href="https://github.com/champkeh/wereadx" target="_blank" class="navBar_link">GitHub</a>
        </div>
    </div>
</div>

<div class="page_container">
    <div class="page_inner">
        <div class="rank_panel">
            <h2 class="title">读书排行榜</h2>
            <div id="rank" class="hide_scrollbar"></div>
        </div>

        <div class="detail_panel">
            <div class="tip_panel">
                <h2 class="title">“自动阅读”功能说明</h2>
                <p>1. 该功能是为了实现微信读书的 <strong>自动阅读</strong>，但是不需要打开 APP 或网页，而是通过服务器后台每隔30分钟更新一次阅读进度。</p>
                <p>2. 注意：该功能并不会更新真实的阅读进度，<strong>只会更新阅读时长</strong>，可用于刷“读书排行榜”或者“阅读挑战赛”。也就是说，添加到这里的书并不会被自动读完。</p>
                <p>3. 你可以在书籍详情页将书籍加入到“自动阅读”中，服务器会自动更新这本书的阅读时长。</p>
                <p>4. 每个用户只能添加一本书，当添加第二本时会自动把之前的自动阅读任务取消。</p>
                <p>5. 该功能目前免费，但是考虑到服务器成本(主要是流量以及kv操作)，后续根据情况可能会降低更新频率或者限制每天只更新3个小时这样。</p>
            </div>
            <div class="task_panel">
                <div class="head">
                    <h2 class="title">正在自动阅读：</h2>
                    <button class="btn" onclick="cancelTask()">停止该任务</button>
                </div>
                <div class="card">
                    <p>
                        <span class="label">书名: </span>
                        <span class="value book"></span>
                    </p>
                    <p>
                        <span class="label">作者: </span>
                        <span class="value author"></span>
                    </p>
                    <p>
                        <span class="label">开始阅读时间: </span>
                        <span class="value start"></span>
                    </p>
                    <p>
                        <span class="label">最后阅读时间: </span>
                        <span class="value recent"></span>
                    </p>
                    <p>
                        <span class="label">今日阅读时长: </span>
                        <span class="value duration"></span>
                    </p>
                </div>
                <p class="empty">当前还没有自动阅读任务~</p>
            </div>
        </div>
    </div>
</div>

<a title="建议反馈" class="feedback" href="https://github.com/champkeh/weread-download/issues/new" target="_blank">
    <svg data-v-98989b36="" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
         class="">
        <path data-v-98989b36=""
              d="M3.26628 5.18082C3.26628 4.35104 3.94716 3.67545 4.7907 3.67545H15.2093C16.0528 3.67545 16.7337 4.35104 16.7337 5.18082V12.538C16.7337 13.3678 16.0528 14.0434 15.2093 14.0434H11.6358C11.4624 14.0434 11.2948 14.1064 11.165 14.221L8.28954 16.7575V14.7452C8.28954 14.356 7.97087 14.0434 7.5814 14.0434H4.7907C3.94716 14.0434 3.26628 13.3678 3.26628 12.538V5.18082ZM4.7907 2.27187C3.16822 2.27187 1.85 3.57264 1.85 5.18082V12.538C1.85 14.1462 3.16822 15.447 4.7907 15.447H6.87326V17.5004C6.87326 18.4274 7.97825 18.9111 8.67204 18.2991L11.9053 15.447H15.2093C16.8318 15.447 18.15 14.1462 18.15 12.538V5.18082C18.15 3.57264 16.8318 2.27187 15.2093 2.27187H4.7907Z"
              fill="#1E80FF" stroke="#1E80FF" stroke-width="0.3" stroke-linejoin="round"></path>
        <path data-v-98989b36=""
              d="M7.00132 9.49204C6.79163 9.17668 6.366 9.09101 6.05063 9.3007C5.73527 9.51038 5.6496 9.93602 5.85929 10.2514C6.74878 11.5891 8.27183 12.4732 10.0017 12.4732C11.7316 12.4732 13.2546 11.5891 14.1441 10.2514C14.3538 9.93602 14.2681 9.51038 13.9528 9.3007C13.6374 9.09101 13.2118 9.17668 13.0021 9.49204C12.356 10.4637 11.2532 11.1018 10.0017 11.1018C8.75017 11.1018 7.64741 10.4637 7.00132 9.49204Z"
              fill="#1E80FF" stroke="#1E80FF" stroke-width="0.3" stroke-linecap="round" stroke-linejoin="round"></path>
    </svg>
</a>

<script>
    function gotoLogin() {
        localStorage.removeItem('token')
        window.location.href = '/login.html'
    }

    function getToken() {
        const token = localStorage.getItem('token')
        if (!token) {
            gotoLogin()
        } else {
            return token
        }
    }

    function fetchRank() {
        const token = getToken()

        fetch('/api/friend/rank', {
            headers: {
                token: token,
            }
        }).then(resp => resp.json()).then(resp => {
            const {code, data, msg} = resp
            if (code === 2) {
                gotoLogin()
                return
            } else if (code !== 0) {
                alert(msg)
                return
            }

            const {ranking} = data
            renderRank(ranking)
        })
    }

    function formatScore(seconds) {
        if (seconds < 60) {
            // 不足一分钟
            return '1分钟'
        } else {
            const minutes = Math.floor(seconds / 60)
            if (minutes < 60) {
                return `${minutes}分钟`
            } else {
                const hour = Math.floor(minutes / 60)
                const minute = minutes % 60
                return `${hour}时${minute}分`
            }
        }
    }

    function renderRank(ranking) {
        const fragment = document.createDocumentFragment()
        for (const item of ranking) {
            const rankItem = document.createElement('div')
            rankItem.className = 'item'
            fragment.append(rankItem)

            const no = document.createElement('span')
            no.textContent = item.rankWeek === 1 ? item.order : ' '
            no.className = 'no'
            rankItem.append(no)

            const avatar = document.createElement('img')
            avatar.src = item.user.avatar
            avatar.alt = '用户头像'
            avatar.className = 'avatar'
            rankItem.append(avatar)

            const name = document.createElement('span')
            name.textContent = item.user.name
            name.className = 'name'
            rankItem.append(name)

            if (item.rankWeek !== 0) {
                const score = document.createElement('span')
                score.textContent = formatScore(item.readingTime) + (item.rankWeek === 2 ? '(上周)' : '')
                score.className = 'score'
                rankItem.append(score)
            }
        }

        document.querySelector('#rank').innerHTML = ''
        document.querySelector('#rank').append(fragment)
    }

    function formatDate(date) {
        return new Intl.DateTimeFormat("zh-CN", {
            dateStyle: "short",
            timeStyle: "medium",
            timeZone: "Asia/Shanghai",
        }).format(date);
    }

    function queryTask() {
        const token = getToken()
        return fetch('/api/task/read/query', {
            headers: {
                token: token,
            }
        }).then(resp => resp.json()).then(resp => {
            const {data} = resp
            if (data) {
                const {book, seconds, createdAt, updatedAt} = data
                document.querySelector('.book').textContent = book.title
                document.querySelector('.author').textContent = book.author
                document.querySelector('.start').textContent = formatDate(new Date(createdAt))
                document.querySelector('.recent').textContent = formatDate(new Date(updatedAt))
                document.querySelector('.duration').textContent = formatScore(seconds) || '--'

                document.querySelector('.card').style.display = 'block'
                document.querySelector('.empty').style.display = 'none'
                document.querySelector('.btn').style.display = 'block'
            } else {
                document.querySelector('.card').style.display = 'none'
                document.querySelector('.empty').style.display = 'block'
                document.querySelector('.btn').style.display = 'none'
            }
        })
    }

    function cancelTask() {
        const token = getToken()
        document.querySelector('.btn').disabled = true
        return fetch('/api/task/read/stop', {
            headers: {
                token: token
            }
        }).then(() => {
            return queryTask()
        }).finally(() => {
            document.querySelector('.btn').disabled = false
        })
    }

    window.addEventListener('DOMContentLoaded', () => {
        fetchRank()
        queryTask()

        setInterval(() => {
            fetchRank()
            queryTask()
        }, 5 * 60 * 1000)
    })
</script>
</body>
</html>

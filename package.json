{"name": "wereadx", "version": "1.0.1", "description": "微信读书辅助工具，可用于书籍下载及自动阅读、自动兑换体验卡等", "repository": "**************:champkeh/wereadx.git", "author": "champ<PERSON>h <<EMAIL>>", "license": "MIT", "scripts": {"dev": "deno run -A --unstable src/server.ts local", "serve": "deno run -A --unstable src/server.ts", "check:type": "deno check --unstable src/**/*.ts", "lint": "deno lint src/**/*.ts", "fmt": "deno fmt src/**/*.ts", "compile:pre-push": "deno compile -A --output .husky/pre-push scripts/pre-push.ts", "prepare": "husky install", "log": "deno run -A --unstable scripts/pre-push.ts", "example:jdread": "deno run -A --unstable examples/jdread/index.ts"}, "devDependencies": {"husky": "^8.0.0"}, "dependencies": {"crypto-js": "^4.2.0", "uuid": "^9.0.1"}}
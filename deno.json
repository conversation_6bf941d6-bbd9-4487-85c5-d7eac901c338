{"tasks": {"dev": "deno run -A --unstable src/server.ts local", "serve": "deno run -A --unstable src/server.ts", "check:type": "deno check --unstable src/**/*.ts", "lint": "deno lint src/**/*.ts", "fmt": "deno fmt src/**/*.ts", "compile:pre-push": "deno compile -A --output .husky/pre-push scripts/pre-push.ts", "prepare": "husky install", "log": "deno run -A --unstable scripts/pre-push.ts", "example:jdread": "deno run -A --unstable examples/jdread/index.ts"}}